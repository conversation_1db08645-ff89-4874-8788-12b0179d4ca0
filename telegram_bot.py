import os
import json
import sqlite3
import logging
from typing import Dict, List
from telegram import Update, InlineKeyboardButton, InlineKeyboardMarkup
from telegram.ext import (
    Application, CommandHandler, MessageHandler, CallbackQueryHandler,
    ContextTypes, filters, ConversationHandler
)
from recommendation_engine import RecommendationEngine, UserPreference
import google.generativeai as genai

# تنظیم لاگ
logging.basicConfig(
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    level=logging.INFO
)
logger = logging.getLogger(__name__)

# مراحل مکالمه
GENRE_SELECTION, RATING_SELECTION, LANGUAGE_PREFERENCE, YEAR_PREFERENCE = range(4)

class TelegramMovieBot:
    def __init__(self, telegram_token: str, gemini_api_key: str):
        self.telegram_token = telegram_token
        self.recommendation_engine = RecommendationEngine()
        
        # تنظیم Gemini AI
        genai.configure(api_key=gemini_api_key)
        self.gemini_model = genai.GenerativeModel('gemini-pro')
        
        # ایجاد جدول کاربران
        self.setup_user_database()
        
        # ژانرهای موجود
        self.available_genres = [
            'اکشن', 'درام', 'کمدی', 'ترسناک', 'علمی تخیلی', 'ماجراجویی',
            'عاشقانه', 'جنایی', 'تریلر', 'انیمیشن', 'مستند', 'جنگی'
        ]
    
    def setup_user_database(self):
        """ایجاد جدول کاربران"""
        conn = sqlite3.connect('users.db')
        cursor = conn.cursor()
        
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS users (
                user_id INTEGER PRIMARY KEY,
                username TEXT,
                first_name TEXT,
                preferred_genres TEXT,
                min_rating REAL DEFAULT 6.0,
                language_preference TEXT DEFAULT 'both',
                preferred_years TEXT,
                watched_movies TEXT DEFAULT '[]',
                liked_movies TEXT DEFAULT '[]',
                disliked_movies TEXT DEFAULT '[]',
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        conn.commit()
        conn.close()
    
    async def start(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """شروع ربات"""
        user = update.effective_user
        
        # ذخیره اطلاعات کاربر
        self.save_user_info(user.id, user.username, user.first_name)
        
        welcome_text = f"""
🎬 سلام {user.first_name}! به ربات پیشنهاد فیلم و سریال خوش آمدید!

من می‌تونم:
🔍 فیلم و سریال مناسب سلیقه‌تون پیدا کنم
⭐ بهترین فیلم‌های هر ژانر رو معرفی کنم
📈 فیلم‌های ترند روز رو نشون بدم
💬 با شما درباره فیلم‌ها گفتگو کنم

برای شروع، لطفاً ترجیحات خودتون رو تنظیم کنید:
/setup - تنظیم ترجیحات
/recommend - دریافت پیشنهاد
/trending - فیلم‌های ترند
/search - جستجوی فیلم
/help - راهنما
        """
        
        await update.message.reply_text(welcome_text)
    
    async def setup_preferences(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """شروع تنظیم ترجیحات کاربر"""
        keyboard = []
        
        # ایجاد دکمه‌های ژانر (2 تا در هر ردیف)
        for i in range(0, len(self.available_genres), 2):
            row = []
            for j in range(2):
                if i + j < len(self.available_genres):
                    genre = self.available_genres[i + j]
                    row.append(InlineKeyboardButton(genre, callback_data=f"genre_{genre}"))
            keyboard.append(row)
        
        keyboard.append([InlineKeyboardButton("✅ تایید انتخاب", callback_data="genre_done")])
        
        reply_markup = InlineKeyboardMarkup(keyboard)
        
        await update.message.reply_text(
            "🎭 لطفاً ژانرهای مورد علاقه‌تان را انتخاب کنید:",
            reply_markup=reply_markup
        )
        
        context.user_data['selected_genres'] = []
        return GENRE_SELECTION
    
    async def handle_genre_selection(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """مدیریت انتخاب ژانر"""
        query = update.callback_query
        await query.answer()
        
        if query.data.startswith("genre_"):
            genre = query.data.replace("genre_", "")
            
            if genre == "done":
                if not context.user_data.get('selected_genres'):
                    await query.edit_message_text("❌ لطفاً حداقل یک ژانر انتخاب کنید.")
                    return GENRE_SELECTION
                
                # انتقال به مرحله بعد
                keyboard = [
                    [InlineKeyboardButton("6+", callback_data="rating_6")],
                    [InlineKeyboardButton("7+", callback_data="rating_7")],
                    [InlineKeyboardButton("8+", callback_data="rating_8")],
                    [InlineKeyboardButton("9+", callback_data="rating_9")]
                ]
                reply_markup = InlineKeyboardMarkup(keyboard)
                
                await query.edit_message_text(
                    "⭐ حداقل امتیاز IMDB مورد نظرتان چیست؟",
                    reply_markup=reply_markup
                )
                return RATING_SELECTION
            
            else:
                # اضافه/حذف ژانر از لیست
                selected_genres = context.user_data.get('selected_genres', [])
                
                if genre in selected_genres:
                    selected_genres.remove(genre)
                else:
                    selected_genres.append(genre)
                
                context.user_data['selected_genres'] = selected_genres
                
                # بروزرسانی دکمه‌ها
                keyboard = []
                for i in range(0, len(self.available_genres), 2):
                    row = []
                    for j in range(2):
                        if i + j < len(self.available_genres):
                            genre_name = self.available_genres[i + j]
                            prefix = "✅ " if genre_name in selected_genres else ""
                            row.append(InlineKeyboardButton(
                                f"{prefix}{genre_name}",
                                callback_data=f"genre_{genre_name}"
                            ))
                    keyboard.append(row)
                
                keyboard.append([InlineKeyboardButton("✅ تایید انتخاب", callback_data="genre_done")])
                reply_markup = InlineKeyboardMarkup(keyboard)
                
                await query.edit_message_reply_markup(reply_markup=reply_markup)
        
        return GENRE_SELECTION
    
    async def handle_rating_selection(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """مدیریت انتخاب امتیاز"""
        query = update.callback_query
        await query.answer()
        
        rating = float(query.data.replace("rating_", ""))
        context.user_data['min_rating'] = rating
        
        keyboard = [
            [InlineKeyboardButton("🎤 دوبله فارسی", callback_data="lang_dub")],
            [InlineKeyboardButton("📝 زیرنویس فارسی", callback_data="lang_sub")],
            [InlineKeyboardButton("🔄 هر دو", callback_data="lang_both")]
        ]
        reply_markup = InlineKeyboardMarkup(keyboard)
        
        await query.edit_message_text(
            "🗣️ ترجیح زبان شما چیست؟",
            reply_markup=reply_markup
        )
        
        return LANGUAGE_PREFERENCE
    
    async def handle_language_preference(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """مدیریت انتخاب زبان"""
        query = update.callback_query
        await query.answer()
        
        lang_pref = query.data.replace("lang_", "")
        context.user_data['language_preference'] = lang_pref
        
        # ذخیره ترجیحات کاربر
        user_id = update.effective_user.id
        self.save_user_preferences(
            user_id,
            context.user_data['selected_genres'],
            context.user_data['min_rating'],
            lang_pref
        )
        
        await query.edit_message_text(
            "✅ ترجیحات شما با موفقیت ذخیره شد!\n\n"
            "حالا می‌تونید از دستورات زیر استفاده کنید:\n"
            "/recommend - دریافت پیشنهاد\n"
            "/trending - فیلم‌های ترند\n"
            "/search - جستجوی فیلم"
        )
        
        return ConversationHandler.END
    
    async def get_recommendations(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """دریافت پیشنهادات"""
        user_id = update.effective_user.id
        user_prefs = self.get_user_preferences(user_id)
        
        if not user_prefs:
            await update.message.reply_text(
                "❌ لطفاً ابتدا ترجیحات خود را تنظیم کنید: /setup"
            )
            return
        
        # دریافت پیشنهادات
        recommendations = self.recommendation_engine.collaborative_filtering(user_prefs, top_k=5)
        
        if not recommendations:
            await update.message.reply_text("متأسفانه فیلم مناسبی پیدا نشد. لطفاً ترجیحات خود را تغییر دهید.")
            return
        
        await update.message.reply_text("🎬 پیشنهادات ویژه برای شما:")
        
        for movie_id in recommendations:
            await self.send_movie_card(update, movie_id)
    
    async def send_movie_card(self, update: Update, movie_id: int):
        """ارسال کارت فیلم"""
        movie = self.recommendation_engine.get_movie_details(movie_id)
        
        if not movie:
            return
        
        # متن کارت فیلم
        card_text = f"""
🎬 **{movie['title_fa']}**
🇺🇸 {movie['title_en']} ({movie['year']})

⭐ امتیاز IMDB: {movie['imdb_rating']}/10
👥 تعداد رای: {movie['imdb_votes']:,}

🎭 ژانر: {', '.join(movie['genres'])}
🎬 کارگردان: {', '.join(movie['directors'])}
🎭 بازیگران: {', '.join(movie['actors'][:3])}

📝 خلاصه:
{movie['plot'][:200]}...

🎤 دوبله فارسی: {'✅' if movie['has_persian_dub'] else '❌'}
📝 زیرنویس فارسی: {'✅' if movie['has_persian_sub'] else '❌'}
        """
        
        # دکمه‌های عملیات
        keyboard = [
            [
                InlineKeyboardButton("📥 دانلود", callback_data=f"download_{movie_id}"),
                InlineKeyboardButton("❤️ پسندیدم", callback_data=f"like_{movie_id}")
            ],
            [
                InlineKeyboardButton("👎 نپسندیدم", callback_data=f"dislike_{movie_id}"),
                InlineKeyboardButton("🔗 مشابه", callback_data=f"similar_{movie_id}")
            ]
        ]
        reply_markup = InlineKeyboardMarkup(keyboard)
        
        await update.message.reply_text(
            card_text,
            reply_markup=reply_markup,
            parse_mode='Markdown'
        )
    
    async def handle_movie_actions(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """مدیریت عملیات روی فیلم‌ها"""
        query = update.callback_query
        await query.answer()

        action, movie_id = query.data.split("_", 1)

        # بررسی اینکه movie_id عدد باشد
        if not movie_id.isdigit():
            await query.message.reply_text("❌ خطا در شناسه فیلم")
            return

        movie_id = int(movie_id)
        user_id = update.effective_user.id
        
        if action == "download":
            await self.send_download_links(query, movie_id)
        elif action == "like":
            self.update_user_feedback(user_id, movie_id, "like")
            await query.edit_message_reply_markup()
            await query.message.reply_text("❤️ نظر شما ثبت شد!")
        elif action == "dislike":
            self.update_user_feedback(user_id, movie_id, "dislike")
            await query.edit_message_reply_markup()
            await query.message.reply_text("👎 نظر شما ثبت شد!")
        elif action == "similar":
            similar_movies = self.recommendation_engine.content_based_recommendation(movie_id, top_k=3)
            await query.message.reply_text("🔗 فیلم‌های مشابه:")
            for similar_id in similar_movies:
                await self.send_movie_card(update, similar_id)
    
    async def send_download_links(self, query, movie_id: int):
        """ارسال لینک‌های دانلود"""
        # دریافت لینک‌های دانلود از پایگاه داده
        conn = sqlite3.connect('movies.db')
        cursor = conn.cursor()
        
        cursor.execute("SELECT download_links FROM movies WHERE id = ?", (movie_id,))
        result = cursor.fetchone()
        conn.close()
        
        if result and result[0]:
            download_links = json.loads(result[0])
            
            text = "📥 **لینک‌های دانلود:**\n\n"
            
            for quality, links in download_links.items():
                text += f"🎥 **{quality}:**\n"
                for i, link in enumerate(links[:3], 1):  # حداکثر 3 لینک
                    text += f"[لینک {i}]({link})\n"
                text += "\n"
            
            await query.edit_message_text(text, parse_mode='Markdown')
        else:
            await query.edit_message_text("❌ لینک دانلودی موجود نیست.")
    
    def save_user_info(self, user_id: int, username: str, first_name: str):
        """ذخیره اطلاعات کاربر"""
        conn = sqlite3.connect('users.db')
        cursor = conn.cursor()
        
        cursor.execute('''
            INSERT OR REPLACE INTO users (user_id, username, first_name)
            VALUES (?, ?, ?)
        ''', (user_id, username, first_name))
        
        conn.commit()
        conn.close()
    
    def save_user_preferences(self, user_id: int, genres: List[str], min_rating: float, lang_pref: str):
        """ذخیره ترجیحات کاربر"""
        conn = sqlite3.connect('users.db')
        cursor = conn.cursor()
        
        cursor.execute('''
            UPDATE users SET 
                preferred_genres = ?,
                min_rating = ?,
                language_preference = ?
            WHERE user_id = ?
        ''', (json.dumps(genres, ensure_ascii=False), min_rating, lang_pref, user_id))
        
        conn.commit()
        conn.close()
    
    def get_user_preferences(self, user_id: int) -> UserPreference:
        """دریافت ترجیحات کاربر"""
        conn = sqlite3.connect('users.db')
        cursor = conn.cursor()
        
        cursor.execute('''
            SELECT preferred_genres, min_rating, language_preference,
                   watched_movies, liked_movies, disliked_movies
            FROM users WHERE user_id = ?
        ''', (user_id,))
        
        result = cursor.fetchone()
        conn.close()
        
        if result:
            return UserPreference(
                user_id=user_id,
                preferred_genres=json.loads(result[0]) if result[0] else [],
                min_rating=result[1] or 6.0,
                language_preference=result[2] or 'both',
                preferred_years=[2020, 2021, 2022, 2023, 2024],
                watched_movies=json.loads(result[3]) if result[3] else [],
                liked_movies=json.loads(result[4]) if result[4] else [],
                disliked_movies=json.loads(result[5]) if result[5] else []
            )
        return None
    
    def update_user_feedback(self, user_id: int, movie_id: int, feedback_type: str):
        """بروزرسانی بازخورد کاربر"""
        conn = sqlite3.connect('users.db')
        cursor = conn.cursor()
        
        # دریافت لیست‌های فعلی
        cursor.execute('SELECT liked_movies, disliked_movies FROM users WHERE user_id = ?', (user_id,))
        result = cursor.fetchone()
        
        if result:
            liked = json.loads(result[0]) if result[0] else []
            disliked = json.loads(result[1]) if result[1] else []
            
            if feedback_type == "like":
                if movie_id not in liked:
                    liked.append(movie_id)
                if movie_id in disliked:
                    disliked.remove(movie_id)
            elif feedback_type == "dislike":
                if movie_id not in disliked:
                    disliked.append(movie_id)
                if movie_id in liked:
                    liked.remove(movie_id)
            
            cursor.execute('''
                UPDATE users SET liked_movies = ?, disliked_movies = ?
                WHERE user_id = ?
            ''', (json.dumps(liked), json.dumps(disliked), user_id))
            
            conn.commit()
        
        conn.close()
    
    def run(self):
        """اجرای ربات"""
        application = Application.builder().token(self.telegram_token).build()
        
        # تنظیم handlers
        conv_handler = ConversationHandler(
            entry_points=[CommandHandler("setup", self.setup_preferences)],
            states={
                GENRE_SELECTION: [CallbackQueryHandler(self.handle_genre_selection)],
                RATING_SELECTION: [CallbackQueryHandler(self.handle_rating_selection)],
                LANGUAGE_PREFERENCE: [CallbackQueryHandler(self.handle_language_preference)],
            },
            fallbacks=[]
        )
        
        application.add_handler(CommandHandler("start", self.start))
        application.add_handler(conv_handler)
        application.add_handler(CommandHandler("recommend", self.get_recommendations))
        application.add_handler(CallbackQueryHandler(self.handle_movie_actions))
        
        # شروع ربات
        application.run_polling()

if __name__ == "__main__":
    # تنظیم توکن‌ها
    TELEGRAM_TOKEN = "YOUR_TELEGRAM_BOT_TOKEN"
    GEMINI_API_KEY = "YOUR_GEMINI_API_KEY"
    
    bot = TelegramMovieBot(TELEGRAM_TOKEN, GEMINI_API_KEY)
    bot.run()
