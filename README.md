# 🎬 ربات تلگرام پیشنهاد فیلم و سریال

ربات هوشمند تلگرامی که با استفاده از هوش مصنوعی، فیلم و سریال مناسب سلیقه کاربران را پیشنهاد می‌دهد.

## ✨ ویژگی‌ها

- 🤖 **گفتگوی طبیعی** با کاربران
- 🎯 **پیشنهاد هوشمند** بر اساس سلیقه کاربر
- 📊 **سیستم امتیازدهی** پیشرفته
- 🔍 **جستجوی قدرتمند** در فیلم‌ها
- 📥 **لینک‌های دانلود** در کیفیت‌های مختلف
- 🎭 **دسته‌بندی ژانر** کامل
- 🗣️ **پشتیبانی از دوبله و زیرنویس** فارسی
- 📈 **فیلم‌های ترند** روز
- ❤️ **سیستم بازخورد** کاربران

## 🏗️ معماری

```
📦 Movie Recommendation Bot
├── 🗃️ data_processor.py      # پردازش فایل‌های HTML
├── 🧠 recommendation_engine.py # موتور پیشنهاد
├── 🤖 telegram_bot.py        # ربات تلگرام
├── ⚙️ config.py             # تنظیمات
├── 🚀 main.py               # اسکریپت اصلی
├── 📋 requirements.txt      # وابستگی‌ها
└── 📚 README.md            # مستندات
```

## 🛠️ نصب و راه‌اندازی

### پیش‌نیازها

- Python 3.8+
- فایل‌های HTML سریال‌ها
- توکن ربات تلگرام
- کلید API Google Gemini

### مرحله 1: کلون پروژه

```bash
git clone <repository-url>
cd movie-recommendation-bot
```

### مرحله 2: نصب وابستگی‌ها

```bash
pip install -r requirements.txt
```

### مرحله 3: تنظیم متغیرهای محیطی

```bash
# Windows
set TELEGRAM_BOT_TOKEN=your_telegram_bot_token
set GEMINI_API_KEY=your_gemini_api_key

# Linux/Mac
export TELEGRAM_BOT_TOKEN=your_telegram_bot_token
export GEMINI_API_KEY=your_gemini_api_key
```

### مرحله 4: دریافت توکن‌ها

#### توکن ربات تلگرام:
1. به [@BotFather](https://t.me/botfather) در تلگرام پیام دهید
2. دستور `/newbot` را ارسال کنید
3. نام و username ربات را انتخاب کنید
4. توکن دریافتی را کپی کنید

#### کلید API Gemini:
1. به [Google AI Studio](https://makersuite.google.com/app/apikey) بروید
2. یک API Key جدید ایجاد کنید
3. کلید را کپی کنید

### مرحله 5: راه‌اندازی

```bash
# راه‌اندازی کامل (پردازش داده‌ها + اجرای ربات)
python main.py --setup

# یا به صورت جداگانه:
python main.py --process-data  # پردازش فایل‌های HTML
python main.py --run-bot       # اجرای ربات
```

## 📖 راهنمای استفاده

### دستورات ربات

- `/start` - شروع کار با ربات
- `/setup` - تنظیم ترجیحات کاربری
- `/recommend` - دریافت پیشنهاد فیلم
- `/trending` - فیلم‌های ترند
- `/search <نام فیلم>` - جستجوی فیلم
- `/help` - راهنمای کامل

### نحوه کار

1. **شروع**: کاربر با `/start` ربات را شروع می‌کند
2. **تنظیمات**: با `/setup` ترجیحات خود را مشخص می‌کند:
   - ژانرهای مورد علاقه
   - حداقل امتیاز IMDB
   - ترجیح زبان (دوبله/زیرنویس)
3. **پیشنهاد**: با `/recommend` فیلم‌های مناسب دریافت می‌کند
4. **تعامل**: می‌تواند فیلم‌ها را لایک/دیسلایک کند
5. **دانلود**: لینک‌های دانلود را دریافت می‌کند

## 🧠 الگوریتم پیشنهاد

### 1. فیلتر همکاری (Collaborative Filtering)
- تحلیل ترجیحات کاربر
- فیلتر بر اساس ژانر، امتیاز، زبان
- امتیازدهی ترکیبی

### 2. فیلتر محتوایی (Content-Based)
- تحلیل ویژگی‌های فیلم
- استفاده از TF-IDF
- محاسبه شباهت کسینوسی

### 3. سیستم ترکیبی (Hybrid)
- ترکیب هر دو روش
- وزن‌دهی هوشمند
- بهینه‌سازی نتایج

## 📊 ساختار داده‌ها

### جدول فیلم‌ها
```sql
CREATE TABLE movies (
    id INTEGER PRIMARY KEY,
    title_fa TEXT,           -- عنوان فارسی
    title_en TEXT,           -- عنوان انگلیسی
    year INTEGER,            -- سال انتشار
    genres TEXT,             -- ژانرها (JSON)
    imdb_rating REAL,        -- امتیاز IMDB
    imdb_votes INTEGER,      -- تعداد رای
    plot TEXT,               -- خلاصه داستان
    poster_url TEXT,         -- لینک پوستر
    directors TEXT,          -- کارگردانان (JSON)
    actors TEXT,             -- بازیگران (JSON)
    download_links TEXT,     -- لینک‌های دانلود (JSON)
    has_persian_dub BOOLEAN, -- دوبله فارسی
    has_persian_sub BOOLEAN  -- زیرنویس فارسی
);
```

### جدول کاربران
```sql
CREATE TABLE users (
    user_id INTEGER PRIMARY KEY,
    preferred_genres TEXT,    -- ژانرهای مورد علاقه
    min_rating REAL,         -- حداقل امتیاز
    language_preference TEXT, -- ترجیح زبان
    watched_movies TEXT,     -- فیلم‌های دیده شده
    liked_movies TEXT,       -- فیلم‌های پسندیده
    disliked_movies TEXT     -- فیلم‌های نپسندیده
);
```

## 🔧 تنظیمات پیشرفته

### فایل config.py
```python
# تنظیمات موتور پیشنهاد
MAX_RECOMMENDATIONS = 10
MIN_IMDB_RATING = 5.0

# تنظیمات ربات
MAX_DOWNLOAD_LINKS_PER_QUALITY = 3
PLOT_PREVIEW_LENGTH = 200

# تنظیمات AI
GEMINI_MODEL_NAME = 'gemini-pro'
MAX_AI_RESPONSE_LENGTH = 1000
```

## 🚀 بهینه‌سازی عملکرد

### 1. کش Redis (اختیاری)
```python
ENABLE_REDIS_CACHE = True
REDIS_HOST = 'localhost'
REDIS_PORT = 6379
```

### 2. ایندکس‌گذاری پایگاه داده
```sql
CREATE INDEX idx_movies_rating ON movies(imdb_rating);
CREATE INDEX idx_movies_year ON movies(year);
CREATE INDEX idx_movies_genres ON movies(genres);
```

### 3. بهینه‌سازی TF-IDF
```python
TfidfVectorizer(
    max_features=5000,
    ngram_range=(1, 2),
    min_df=2,
    max_df=0.8
)
```

## 🐛 عیب‌یابی

### مشکلات رایج

1. **خطای توکن نامعتبر**
   ```
   ❌ خطا: Invalid token
   💡 راه‌حل: توکن ربات را از @BotFather مجدداً دریافت کنید
   ```

2. **خطای API Gemini**
   ```
   ❌ خطا: API key not valid
   💡 راه‌حل: کلید API را از Google AI Studio بررسی کنید
   ```

3. **فایل‌های HTML پیدا نشد**
   ```
   ❌ خطا: HTML folder not found
   💡 راه‌حل: مسیر فولدر series_pages را بررسی کنید
   ```

### لاگ‌ها
```bash
# مشاهده لاگ‌ها
tail -f bot.log

# تنظیم سطح لاگ
export LOG_LEVEL=DEBUG
```

## 📈 آمار و گزارش

### نمایش وضعیت
```bash
python main.py --status
```

خروجی:
```
📊 وضعیت پروژه:
   📁 مسیر فایل‌های HTML: series_pages
   🗃️ پایگاه داده فیلم‌ها: ✅ موجود
   📄 تعداد فایل‌های HTML: 847
   🔑 توکن تلگرام: ✅ تنظیم شده
   🤖 کلید Gemini: ✅ تنظیم شده
```

## 🤝 مشارکت

1. Fork کنید
2. Branch جدید بسازید (`git checkout -b feature/amazing-feature`)
3. تغییرات را commit کنید (`git commit -m 'Add amazing feature'`)
4. Push کنید (`git push origin feature/amazing-feature`)
5. Pull Request ایجاد کنید

## 📄 مجوز

این پروژه تحت مجوز MIT منتشر شده است.

## 📞 پشتیبانی

- 🐛 گزارش باگ: [Issues](https://github.com/your-repo/issues)
- 💬 سوالات: [Discussions](https://github.com/your-repo/discussions)
- 📧 ایمیل: <EMAIL>

---

**ساخته شده با ❤️ برای علاقه‌مندان سینما و سریال**
