import os
import json
import sqlite3
import logging
from typing import Dict, List, Optional
from dataclasses import dataclass

from telegram import Update, InlineKeyboardButton, InlineKeyboardMarkup, ReplyKeyboardMarkup, KeyboardButton
from telegram.ext import Application, CommandHandler, MessageHandler, filters, ContextTypes, CallbackQueryHandler

import google.generativeai as genai
from recommendation_engine import RecommendationEngine, UserPreference

# تنظیم لاگ
logging.basicConfig(
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    level=logging.INFO
)
logger = logging.getLogger(__name__)

class SmartTelegramBot:
    def __init__(self, telegram_token: str, gemini_api_key: str):
        self.telegram_token = telegram_token
        self.gemini_api_key = gemini_api_key
        
        # تنظیم Gemini AI
        genai.configure(api_key=gemini_api_key)
        self.model = genai.GenerativeModel('gemini-pro')
        
        # تنظیم موتور پیشنهاد
        self.recommendation_engine = RecommendationEngine()
        
        # ذخیره وضعیت گفتگو کاربران
        self.user_conversations = {}
    
    def get_main_menu_keyboard(self):
        """منوی اصلی ربات"""
        keyboard = [
            [KeyboardButton("🎬 پیشنهاد فیلم"), KeyboardButton("🔍 جستجوی فیلم")],
            [KeyboardButton("⭐ فیلم‌های برتر"), KeyboardButton("🆕 جدیدترین فیلم‌ها")],
            [KeyboardButton("📊 آمار من"), KeyboardButton("⚙️ تنظیمات")]
        ]
        return ReplyKeyboardMarkup(keyboard, resize_keyboard=True)
    
    async def start(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """شروع ربات"""
        user = update.effective_user
        welcome_text = f"""
🎬 سلام {user.first_name}! به ربات هوشمند پیشنهاد فیلم خوش آمدید!

🤖 من یک دستیار هوشمند هستم که می‌توانم:
• با شما گفتگو کنم و سوالاتتان را پاسخ دهم
• بهترین فیلم‌ها و سریال‌ها را پیشنهاد دهم
• فیلم‌ها را جستجو کنم
• لینک‌های دانلود ارائه دهم

💬 شما می‌توانید:
✅ از منو استفاده کنید
✅ یا مستقیماً با من صحبت کنید

مثال: "یک فیلم اکشن خوب پیشنهاد بده" یا "فیلم کمدی می‌خوام"
        """
        
        await update.message.reply_text(
            welcome_text,
            reply_markup=self.get_main_menu_keyboard()
        )
    
    async def handle_message(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """مدیریت پیام‌های کاربر"""
        user_id = update.effective_user.id
        message_text = update.message.text
        
        # بررسی دکمه‌های منو
        if message_text == "🎬 پیشنهاد فیلم":
            await self.recommend_movies(update, context)
        elif message_text == "🔍 جستجوی فیلم":
            await self.search_movies(update, context)
        elif message_text == "⭐ فیلم‌های برتر":
            await self.top_movies(update, context)
        elif message_text == "🆕 جدیدترین فیلم‌ها":
            await self.latest_movies(update, context)
        elif message_text == "📊 آمار من":
            await self.user_stats(update, context)
        elif message_text == "⚙️ تنظیمات":
            await self.settings(update, context)
        else:
            # گفتگوی هوشمند با AI
            await self.ai_conversation(update, context)
    
    async def ai_conversation(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """گفتگوی هوشمند با کاربر"""
        user_message = update.message.text
        user_id = update.effective_user.id
        
        # ارسال پیام "در حال تایپ..."
        await context.bot.send_chat_action(chat_id=update.effective_chat.id, action="typing")
        
        try:
            # ایجاد prompt برای AI
            prompt = f"""
شما یک دستیار هوشمند فیلم و سریال هستید. کاربر این پیام را فرستاده: "{user_message}"

اگر کاربر درخواست پیشنهاد فیلم یا سریال دارد، پاسخ مناسب بدهید و بگویید که می‌توانید فیلم‌های مناسب پیدا کنید.
اگر سوال عمومی دارد، پاسخ مفیدی بدهید.
پاسخ شما باید:
- فارسی باشد
- دوستانه و مفید باشد
- حداکثر 200 کلمه باشد
- اگر درباره فیلم است، پیشنهاد دهید که از دکمه "🎬 پیشنهاد فیلم" استفاده کند

پاسخ:
            """
            
            response = self.model.generate_content(prompt)
            ai_response = response.text
            
            # بررسی اینکه آیا کاربر درخواست فیلم دارد
            movie_keywords = ['فیلم', 'سریال', 'پیشنهاد', 'ببینم', 'تماشا', 'دانلود']
            if any(keyword in user_message for keyword in movie_keywords):
                # اضافه کردن دکمه پیشنهاد سریع
                keyboard = [[InlineKeyboardButton("🎬 پیشنهاد فیلم", callback_data="quick_recommend")]]
                reply_markup = InlineKeyboardMarkup(keyboard)
                
                await update.message.reply_text(
                    ai_response + "\n\n💡 برای پیشنهاد سریع روی دکمه زیر کلیک کنید:",
                    reply_markup=reply_markup
                )
            else:
                await update.message.reply_text(ai_response)
                
        except Exception as e:
            logger.error(f"خطا در AI conversation: {e}")
            await update.message.reply_text(
                "❌ متأسفانه در حال حاضر مشکلی در پردازش پیام شما وجود دارد.\n"
                "لطفاً از منوی اصلی استفاده کنید یا دوباره تلاش کنید."
            )
    
    async def recommend_movies(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """پیشنهاد فیلم"""
        await update.message.reply_text("🎯 در حال جستجوی بهترین فیلم‌ها برای شما...")
        
        # دریافت فیلم‌های تصادفی از پایگاه داده
        conn = sqlite3.connect('movies.db')
        cursor = conn.cursor()
        
        cursor.execute("""
            SELECT id, title_fa, title_en, year, imdb_rating, poster_url 
            FROM movies 
            WHERE imdb_rating > 7.0 
            ORDER BY RANDOM() 
            LIMIT 3
        """)
        
        movies = cursor.fetchall()
        conn.close()
        
        if movies:
            await update.message.reply_text("🎬 پیشنهادات ویژه برای شما:")
            for movie in movies:
                await self.send_movie_card(update, movie)
        else:
            await update.message.reply_text("❌ متأسفانه فیلمی پیدا نشد.")
    
    async def send_movie_card(self, update: Update, movie_data):
        """ارسال کارت فیلم با پوستر"""
        movie_id, title_fa, title_en, year, imdb_rating, poster_url = movie_data
        
        # متن کارت فیلم
        card_text = f"""
🎬 **{title_fa}**
🇺🇸 {title_en} ({year})

⭐ امتیاز IMDB: {imdb_rating}/10

💬 برای دریافت اطلاعات بیشتر و لینک‌های دانلود روی دکمه‌ها کلیک کنید.
        """
        
        # دکمه‌های تعاملی
        keyboard = [
            [
                InlineKeyboardButton("📥 دانلود", callback_data=f"download_{movie_id}"),
                InlineKeyboardButton("📖 اطلاعات بیشتر", callback_data=f"details_{movie_id}")
            ],
            [
                InlineKeyboardButton("❤️ پسندیدم", callback_data=f"like_{movie_id}"),
                InlineKeyboardButton("👎 نپسندیدم", callback_data=f"dislike_{movie_id}")
            ]
        ]
        reply_markup = InlineKeyboardMarkup(keyboard)
        
        try:
            if poster_url and poster_url.startswith('http'):
                # ارسال با پوستر
                await update.message.reply_photo(
                    photo=poster_url,
                    caption=card_text,
                    reply_markup=reply_markup,
                    parse_mode='Markdown'
                )
            else:
                # ارسال بدون پوستر
                await update.message.reply_text(
                    card_text,
                    reply_markup=reply_markup,
                    parse_mode='Markdown'
                )
        except Exception as e:
            logger.error(f"خطا در ارسال پوستر: {e}")
            # ارسال بدون پوستر در صورت خطا
            await update.message.reply_text(
                card_text,
                reply_markup=reply_markup,
                parse_mode='Markdown'
            )
    
    async def search_movies(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """جستجوی فیلم"""
        await update.message.reply_text(
            "🔍 نام فیلم یا سریالی که می‌خواهید جستجو کنید را بنویسید:\n\n"
            "مثال: Breaking Bad یا بریکینگ بد"
        )
        # ذخیره وضعیت جستجو
        self.user_conversations[update.effective_user.id] = "searching"
    
    async def top_movies(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """فیلم‌های برتر"""
        conn = sqlite3.connect('movies.db')
        cursor = conn.cursor()
        
        cursor.execute("""
            SELECT id, title_fa, title_en, year, imdb_rating, poster_url 
            FROM movies 
            ORDER BY imdb_rating DESC 
            LIMIT 5
        """)
        
        movies = cursor.fetchall()
        conn.close()
        
        if movies:
            await update.message.reply_text("⭐ برترین فیلم‌ها:")
            for movie in movies:
                await self.send_movie_card(update, movie)
        else:
            await update.message.reply_text("❌ فیلمی پیدا نشد.")
    
    async def latest_movies(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """جدیدترین فیلم‌ها"""
        conn = sqlite3.connect('movies.db')
        cursor = conn.cursor()
        
        cursor.execute("""
            SELECT id, title_fa, title_en, year, imdb_rating, poster_url 
            FROM movies 
            ORDER BY year DESC 
            LIMIT 5
        """)
        
        movies = cursor.fetchall()
        conn.close()
        
        if movies:
            await update.message.reply_text("🆕 جدیدترین فیلم‌ها:")
            for movie in movies:
                await self.send_movie_card(update, movie)
        else:
            await update.message.reply_text("❌ فیلمی پیدا نشد.")
    
    async def user_stats(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """آمار کاربر"""
        stats_text = """
📊 آمار شما:

👀 فیلم‌های مشاهده شده: 0
❤️ فیلم‌های پسندیده شده: 0
📥 دانلودهای انجام شده: 0

🔜 این قابلیت به زودی تکمیل می‌شود!
        """
        await update.message.reply_text(stats_text)
    
    async def settings(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """تنظیمات"""
        keyboard = [
            [InlineKeyboardButton("🎭 تنظیم ژانر مورد علاقه", callback_data="set_genre")],
            [InlineKeyboardButton("🌟 تنظیم حداقل امتیاز", callback_data="set_rating")],
            [InlineKeyboardButton("🔙 بازگشت به منو", callback_data="back_to_menu")]
        ]
        reply_markup = InlineKeyboardMarkup(keyboard)
        
        await update.message.reply_text(
            "⚙️ تنظیمات ربات:\n\nلطفاً گزینه مورد نظر را انتخاب کنید:",
            reply_markup=reply_markup
        )

    async def handle_callback_query(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """مدیریت کلیک روی دکمه‌ها"""
        query = update.callback_query
        await query.answer()

        data = query.data

        if data == "quick_recommend":
            # پیشنهاد سریع فیلم
            await self.recommend_movies(update, context)

        elif data.startswith("download_"):
            movie_id = int(data.split("_")[1])
            await self.send_download_links(query, movie_id)

        elif data.startswith("details_"):
            movie_id = int(data.split("_")[1])
            await self.send_movie_details(query, movie_id)

        elif data.startswith("like_"):
            movie_id = int(data.split("_")[1])
            await query.edit_message_reply_markup()
            await query.message.reply_text("❤️ نظر شما ثبت شد! ممنون از بازخوردتان.")

        elif data.startswith("dislike_"):
            movie_id = int(data.split("_")[1])
            await query.edit_message_reply_markup()
            await query.message.reply_text("👎 نظر شما ثبت شد! سعی می‌کنیم بهتر پیشنهاد دهیم.")

        elif data == "set_genre":
            await query.message.reply_text("🎭 این قابلیت به زودی اضافه می‌شود!")

        elif data == "set_rating":
            await query.message.reply_text("🌟 این قابلیت به زودی اضافه می‌شود!")

        elif data == "back_to_menu":
            await query.message.reply_text(
                "🏠 به منوی اصلی بازگشتید:",
                reply_markup=self.get_main_menu_keyboard()
            )

    async def send_download_links(self, query, movie_id: int):
        """ارسال لینک‌های دانلود"""
        conn = sqlite3.connect('movies.db')
        cursor = conn.cursor()

        cursor.execute("SELECT download_links, title_fa FROM movies WHERE id = ?", (movie_id,))
        result = cursor.fetchone()
        conn.close()

        if result and result[0]:
            download_links = json.loads(result[0])
            title = result[1]

            text = f"📥 **لینک‌های دانلود {title}:**\n\n"

            for quality, links in download_links.items():
                if links:
                    text += f"🎬 **{quality}:**\n"
                    for i, link in enumerate(links[:3], 1):  # حداکثر 3 لینک
                        text += f"[لینک {i}]({link})\n"
                    text += "\n"

            if len(text) > 4000:  # محدودیت طول پیام تلگرام
                text = text[:4000] + "..."

            await query.message.reply_text(text, parse_mode='Markdown')
        else:
            await query.message.reply_text("❌ لینک دانلودی برای این فیلم موجود نیست.")

    async def send_movie_details(self, query, movie_id: int):
        """ارسال جزئیات کامل فیلم"""
        conn = sqlite3.connect('movies.db')
        cursor = conn.cursor()

        cursor.execute("""
            SELECT title_fa, title_en, year, genres, imdb_rating, imdb_votes,
                   plot, directors, actors, duration, has_persian_dub, has_persian_sub
            FROM movies WHERE id = ?
        """, (movie_id,))

        result = cursor.fetchone()
        conn.close()

        if result:
            title_fa, title_en, year, genres, imdb_rating, imdb_votes, plot, directors, actors, duration, has_dub, has_sub = result

            # پردازش داده‌ها
            genres_list = json.loads(genres) if genres else []
            directors_list = json.loads(directors) if directors else []
            actors_list = json.loads(actors) if actors else []

            details_text = f"""
🎬 **{title_fa}**
🇺🇸 {title_en} ({year})

⭐ امتیاز IMDB: {imdb_rating}/10
👥 تعداد رای: {imdb_votes:,}

🎭 ژانر: {', '.join(genres_list)}
🎬 کارگردان: {', '.join(directors_list)}
🎭 بازیگران: {', '.join(actors_list[:5])}
⏱️ مدت زمان: {duration} دقیقه

🎵 دوبله فارسی: {'✅' if has_dub else '❌'}
📝 زیرنویس فارسی: {'✅' if has_sub else '❌'}

📖 **خلاصه داستان:**
{plot[:300]}{'...' if len(plot) > 300 else ''}
            """

            await query.message.reply_text(details_text, parse_mode='Markdown')
        else:
            await query.message.reply_text("❌ اطلاعات این فیلم موجود نیست.")

    def run(self):
        """اجرای ربات"""
        application = Application.builder().token(self.telegram_token).build()

        # اضافه کردن هندلرها
        application.add_handler(CommandHandler("start", self.start))
        application.add_handler(MessageHandler(filters.TEXT & ~filters.COMMAND, self.handle_message))
        application.add_handler(CallbackQueryHandler(self.handle_callback_query))

        # شروع ربات
        print("🤖 ربات هوشمند در حال اجرا...")
        application.run_polling()

if __name__ == "__main__":
    from config import Config

    config = Config()

    if not config.TELEGRAM_BOT_TOKEN or not config.GEMINI_API_KEY:
        print("❌ لطفاً توکن‌های API را تنظیم کنید")
        exit(1)

    bot = SmartTelegramBot(config.TELEGRAM_BOT_TOKEN, config.GEMINI_API_KEY)
    bot.run()
