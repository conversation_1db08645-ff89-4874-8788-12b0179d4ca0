#!/usr/bin/env python3
"""
اسکریپت اصلی ربات تلگرام پیشنهاد فیلم و سریال
"""

import os
import sys
import logging
import argparse
from pathlib import Path

# اضافه کردن مسیر پروژه به Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from config import config, validate_config
from data_processor import HTMLDataProcessor
from telegram_bot import TelegramMovieBot

def setup_logging():
    """تنظیم سیستم لاگ"""
    logging.basicConfig(
        level=getattr(logging, config.LOG_LEVEL.upper()),
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler(config.LOG_FILE, encoding='utf-8'),
            logging.StreamHandler(sys.stdout)
        ]
    )

def process_html_data():
    """پردازش فایل‌های HTML و ایجاد پایگاه داده"""
    print("🔄 شروع پردازش فایل‌های HTML...")
    
    processor = HTMLDataProcessor(
        html_folder_path=config.HTML_FOLDER_PATH,
        db_path=config.MOVIES_DB_PATH
    )
    
    try:
        processor.process_all_files()
        print("✅ پردازش فایل‌های HTML با موفقیت انجام شد!")
        return True
    except Exception as e:
        print(f"❌ خطا در پردازش فایل‌های HTML: {e}")
        return False

def run_bot():
    """اجرای ربات تلگرام"""
    print("🤖 شروع ربات تلگرام...")
    
    try:
        bot = TelegramMovieBot(
            telegram_token=config.TELEGRAM_BOT_TOKEN,
            gemini_api_key=config.GEMINI_API_KEY
        )
        
        print("✅ ربات آماده است! در حال اجرا...")
        bot.run()
        
    except Exception as e:
        print(f"❌ خطا در اجرای ربات: {e}")
        return False

def check_database_exists():
    """بررسی وجود پایگاه داده فیلم‌ها"""
    return os.path.exists(config.MOVIES_DB_PATH)

def show_status():
    """نمایش وضعیت پروژه"""
    print("📊 وضعیت پروژه:")
    print(f"   📁 مسیر فایل‌های HTML: {config.HTML_FOLDER_PATH}")
    print(f"   🗃️ پایگاه داده فیلم‌ها: {'✅ موجود' if check_database_exists() else '❌ موجود نیست'}")
    
    if os.path.exists(config.HTML_FOLDER_PATH):
        html_files = list(Path(config.HTML_FOLDER_PATH).glob("*.html"))
        print(f"   📄 تعداد فایل‌های HTML: {len(html_files)}")
    else:
        print(f"   📄 مسیر HTML موجود نیست!")
    
    print(f"   🔑 توکن تلگرام: {'✅ تنظیم شده' if config.TELEGRAM_BOT_TOKEN else '❌ تنظیم نشده'}")
    print(f"   🤖 کلید Gemini: {'✅ تنظیم شده' if config.GEMINI_API_KEY else '❌ تنظیم نشده'}")

def main():
    """تابع اصلی"""
    parser = argparse.ArgumentParser(description='ربات تلگرام پیشنهاد فیلم و سریال')
    parser.add_argument('--process-data', action='store_true', 
                       help='پردازش فایل‌های HTML و ایجاد پایگاه داده')
    parser.add_argument('--run-bot', action='store_true',
                       help='اجرای ربات تلگرام')
    parser.add_argument('--status', action='store_true',
                       help='نمایش وضعیت پروژه')
    parser.add_argument('--setup', action='store_true',
                       help='راه‌اندازی کامل پروژه')
    
    args = parser.parse_args()
    
    # تنظیم لاگ
    setup_logging()
    
    # نمایش وضعیت
    if args.status:
        show_status()
        return
    
    # بررسی تنظیمات
    try:
        validate_config()
        print("✅ تنظیمات معتبر است!")
    except ValueError as e:
        print(f"❌ خطا در تنظیمات: {e}")
        print("\n💡 راهنمای تنظیم:")
        print("1. توکن ربات تلگرام را از @BotFather دریافت کنید")
        print("2. کلید API Gemini را از Google AI Studio دریافت کنید")
        print("3. متغیرهای محیطی را تنظیم کنید:")
        print("   export TELEGRAM_BOT_TOKEN='your_token_here'")
        print("   export GEMINI_API_KEY='your_api_key_here'")
        return
    
    # راه‌اندازی کامل
    if args.setup:
        print("🚀 شروع راه‌اندازی کامل پروژه...")
        
        # پردازش داده‌ها
        if not check_database_exists():
            if not process_html_data():
                print("❌ راه‌اندازی متوقف شد به دلیل خطا در پردازش داده‌ها")
                return
        else:
            print("ℹ️ پایگاه داده از قبل موجود است. از پردازش مجدد صرف‌نظر شد.")
        
        # اجرای ربات
        run_bot()
        return
    
    # پردازش داده‌ها
    if args.process_data:
        process_html_data()
        return
    
    # اجرای ربات
    if args.run_bot:
        if not check_database_exists():
            print("❌ پایگاه داده فیلم‌ها موجود نیست!")
            print("💡 ابتدا داده‌ها را پردازش کنید: python main.py --process-data")
            return
        
        run_bot()
        return
    
    # اگر هیچ آرگومانی داده نشده، نمایش راهنما
    parser.print_help()
    print("\n🎬 مثال‌های استفاده:")
    print("  python main.py --setup           # راه‌اندازی کامل")
    print("  python main.py --process-data    # پردازش فایل‌های HTML")
    print("  python main.py --run-bot         # اجرای ربات")
    print("  python main.py --status          # نمایش وضعیت")

if __name__ == "__main__":
    main()
