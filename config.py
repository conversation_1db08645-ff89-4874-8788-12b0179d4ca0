import os
from dataclasses import dataclass

@dataclass
class Config:
    # Telegram Bot Configuration
    TELEGRAM_BOT_TOKEN: str = os.getenv('TELEGRAM_BOT_TOKEN', '**********************************************')

    # Google Gemini AI Configuration
    GEMINI_API_KEY: str = os.getenv('GEMINI_API_KEY', 'AIzaSyCQHPIHGQHOOmse13HqXwrGVo0EPpDePVU')
    
    # Database Configuration
    MOVIES_DB_PATH: str = 'movies.db'
    USERS_DB_PATH: str = 'users.db'
    
    # HTML Files Path
    HTML_FOLDER_PATH: str = 'series_pages'
    
    # Recommendation Engine Settings
    MAX_RECOMMENDATIONS: int = 10
    MIN_IMDB_RATING: float = 5.0
    
    # Bot Settings
    MAX_DOWNLOAD_LINKS_PER_QUALITY: int = 3
    MAX_ACTORS_TO_SHOW: int = 5
    PLOT_PREVIEW_LENGTH: int = 200
    
    # AI Settings
    GEMINI_MODEL_NAME: str = 'gemini-pro'
    MAX_AI_RESPONSE_LENGTH: int = 1000
    
    # Cache Settings
    ENABLE_REDIS_CACHE: bool = False
    REDIS_HOST: str = 'localhost'
    REDIS_PORT: int = 6379
    REDIS_DB: int = 0
    
    # Logging
    LOG_LEVEL: str = 'INFO'
    LOG_FILE: str = 'bot.log'

# ایجاد instance از تنظیمات
config = Config()

# بررسی تنظیمات ضروری
def validate_config():
    """بررسی وجود تنظیمات ضروری"""
    errors = []
    
    if not config.TELEGRAM_BOT_TOKEN:
        errors.append("TELEGRAM_BOT_TOKEN is required")
    
    if not config.GEMINI_API_KEY:
        errors.append("GEMINI_API_KEY is required")
    
    if not os.path.exists(config.HTML_FOLDER_PATH):
        errors.append(f"HTML folder path does not exist: {config.HTML_FOLDER_PATH}")
    
    if errors:
        raise ValueError("Configuration errors:\n" + "\n".join(errors))
    
    return True

if __name__ == "__main__":
    try:
        validate_config()
        print("✅ Configuration is valid!")
    except ValueError as e:
        print(f"❌ Configuration error: {e}")
