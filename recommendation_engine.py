import sqlite3
import json
import numpy as np
from typing import List, Dict, Tuple
from sklearn.feature_extraction.text import TfidfVectorizer
from sklearn.metrics.pairwise import cosine_similarity
from sklearn.preprocessing import StandardScaler
import pandas as pd
from dataclasses import dataclass

@dataclass
class UserPreference:
    user_id: int
    preferred_genres: List[str]
    min_rating: float
    preferred_years: List[int]
    language_preference: str  # 'dub', 'sub', 'both'
    watched_movies: List[int]
    liked_movies: List[int]
    disliked_movies: List[int]

class RecommendationEngine:
    def __init__(self, db_path: str = "movies.db"):
        self.db_path = db_path
        self.movies_df = None
        self.tfidf_vectorizer = None
        self.content_matrix = None
        self.scaler = StandardScaler()
        self.load_movies()
        self.prepare_content_features()
    
    def load_movies(self):
        """بارگذاری فیلم‌ها از پایگاه داده"""
        conn = sqlite3.connect(self.db_path)
        
        query = """
        SELECT id, title_fa, title_en, year, genres, imdb_rating, imdb_votes,
               plot, directors, actors, has_persian_dub, has_persian_sub,
               film2media_rating
        FROM movies
        WHERE imdb_rating > 0
        """
        
        self.movies_df = pd.read_sql_query(query, conn)
        conn.close()
        
        # تبدیل JSON strings به lists
        self.movies_df['genres'] = self.movies_df['genres'].apply(
            lambda x: json.loads(x) if x else []
        )
        self.movies_df['directors'] = self.movies_df['directors'].apply(
            lambda x: json.loads(x) if x else []
        )
        self.movies_df['actors'] = self.movies_df['actors'].apply(
            lambda x: json.loads(x) if x else []
        )
    
    def prepare_content_features(self):
        """آماده‌سازی ویژگی‌های محتوایی برای سیستم پیشنهاد"""
        # ترکیب ویژگی‌های متنی
        content_features = []
        
        for _, movie in self.movies_df.iterrows():
            # ترکیب ژانر، کارگردان، بازیگران و خلاصه
            features = []
            
            # ژانرها (وزن بیشتر)
            if movie['genres']:
                features.extend(movie['genres'] * 3)
            
            # کارگردان (وزن متوسط)
            if movie['directors']:
                features.extend(movie['directors'] * 2)
            
            # بازیگران (وزن کم)
            if movie['actors']:
                features.extend(movie['actors'][:5])  # فقط 5 بازیگر اول
            
            # خلاصه داستان
            if movie['plot']:
                features.append(movie['plot'])
            
            content_features.append(' '.join(features))
        
        # ایجاد ماتریس TF-IDF
        self.tfidf_vectorizer = TfidfVectorizer(
            max_features=5000,
            stop_words=None,  # برای فارسی نیاز به stop words خاص داریم
            ngram_range=(1, 2)
        )
        
        self.content_matrix = self.tfidf_vectorizer.fit_transform(content_features)
    
    def content_based_recommendation(self, movie_id: int, top_k: int = 10) -> List[int]:
        """پیشنهاد بر اساس محتوا"""
        try:
            movie_idx = self.movies_df[self.movies_df['id'] == movie_id].index[0]
            
            # محاسبه شباهت کسینوسی
            similarities = cosine_similarity(
                self.content_matrix[movie_idx:movie_idx+1],
                self.content_matrix
            ).flatten()
            
            # مرتب‌سازی بر اساس شباهت
            similar_indices = similarities.argsort()[::-1][1:top_k+1]
            
            return self.movies_df.iloc[similar_indices]['id'].tolist()
        
        except IndexError:
            return []
    
    def collaborative_filtering(self, user_preferences: UserPreference, top_k: int = 10) -> List[int]:
        """پیشنهاد بر اساس فیلتر همکاری"""
        # فیلتر اولیه بر اساس ترجیحات کاربر
        filtered_movies = self.movies_df.copy()
        
        # فیلتر ژانر
        if user_preferences.preferred_genres:
            filtered_movies = filtered_movies[
                filtered_movies['genres'].apply(
                    lambda x: any(genre in x for genre in user_preferences.preferred_genres)
                )
            ]
        
        # فیلتر امتیاز
        filtered_movies = filtered_movies[
            filtered_movies['imdb_rating'] >= user_preferences.min_rating
        ]
        
        # فیلتر زبان
        if user_preferences.language_preference == 'dub':
            filtered_movies = filtered_movies[filtered_movies['has_persian_dub'] == True]
        elif user_preferences.language_preference == 'sub':
            filtered_movies = filtered_movies[filtered_movies['has_persian_sub'] == True]
        
        # حذف فیلم‌های دیده شده
        filtered_movies = filtered_movies[
            ~filtered_movies['id'].isin(user_preferences.watched_movies)
        ]
        
        # امتیازدهی ترکیبی
        filtered_movies['score'] = self._calculate_hybrid_score(
            filtered_movies, user_preferences
        )
        
        # مرتب‌سازی و انتخاب بهترین‌ها
        top_movies = filtered_movies.nlargest(top_k, 'score')
        
        return top_movies['id'].tolist()
    
    def _calculate_hybrid_score(self, movies_df: pd.DataFrame, user_prefs: UserPreference) -> pd.Series:
        """محاسبه امتیاز ترکیبی"""
        scores = pd.Series(index=movies_df.index, dtype=float)
        
        for idx, movie in movies_df.iterrows():
            score = 0.0
            
            # امتیاز IMDB (وزن 30%)
            score += (movie['imdb_rating'] / 10.0) * 0.3
            
            # تعداد رای (وزن 10%)
            normalized_votes = min(movie['imdb_votes'] / 1000000, 1.0)
            score += normalized_votes * 0.1
            
            # تطابق ژانر (وزن 40%)
            if user_prefs.preferred_genres:
                genre_match = len(set(movie['genres']) & set(user_prefs.preferred_genres))
                genre_score = genre_match / len(user_prefs.preferred_genres)
                score += genre_score * 0.4
            
            # سال انتشار (وزن 10%)
            if user_prefs.preferred_years:
                year_distance = min([abs(movie['year'] - year) for year in user_prefs.preferred_years])
                year_score = max(0, 1 - (year_distance / 20))  # کاهش امتیاز برای فاصله بیشتر از 20 سال
                score += year_score * 0.1
            
            # ترجیح زبان (وزن 10%)
            if user_prefs.language_preference == 'dub' and movie['has_persian_dub']:
                score += 0.1
            elif user_prefs.language_preference == 'sub' and movie['has_persian_sub']:
                score += 0.1
            elif user_prefs.language_preference == 'both':
                if movie['has_persian_dub'] or movie['has_persian_sub']:
                    score += 0.1
            
            scores[idx] = score
        
        return scores
    
    def get_trending_movies(self, top_k: int = 10) -> List[int]:
        """فیلم‌های ترند (بر اساس امتیاز و تعداد رای)"""
        trending_score = (
            self.movies_df['imdb_rating'] * 0.7 +
            (self.movies_df['imdb_votes'] / self.movies_df['imdb_votes'].max()) * 3 * 0.3
        )
        
        top_trending = self.movies_df.nlargest(top_k, trending_score.values)
        return top_trending['id'].tolist()
    
    def get_movies_by_genre(self, genre: str, top_k: int = 10) -> List[int]:
        """فیلم‌های یک ژانر خاص"""
        genre_movies = self.movies_df[
            self.movies_df['genres'].apply(lambda x: genre in x)
        ]
        
        top_genre_movies = genre_movies.nlargest(top_k, 'imdb_rating')
        return top_genre_movies['id'].tolist()
    
    def search_movies(self, query: str, top_k: int = 10) -> List[int]:
        """جستجوی فیلم"""
        query = query.lower()
        
        # جستجو در عنوان فارسی و انگلیسی
        search_results = self.movies_df[
            (self.movies_df['title_fa'].str.lower().str.contains(query, na=False)) |
            (self.movies_df['title_en'].str.lower().str.contains(query, na=False)) |
            (self.movies_df['plot'].str.lower().str.contains(query, na=False))
        ]
        
        # مرتب‌سازی بر اساس امتیاز
        search_results = search_results.nlargest(top_k, 'imdb_rating')
        
        return search_results['id'].tolist()
    
    def get_movie_details(self, movie_id: int) -> Dict:
        """دریافت جزئیات فیلم"""
        movie = self.movies_df[self.movies_df['id'] == movie_id]
        
        if movie.empty:
            return None
        
        movie_data = movie.iloc[0]
        
        return {
            'id': movie_data['id'],
            'title_fa': movie_data['title_fa'],
            'title_en': movie_data['title_en'],
            'year': movie_data['year'],
            'genres': movie_data['genres'],
            'imdb_rating': movie_data['imdb_rating'],
            'imdb_votes': movie_data['imdb_votes'],
            'plot': movie_data['plot'],
            'directors': movie_data['directors'],
            'actors': movie_data['actors'],
            'has_persian_dub': movie_data['has_persian_dub'],
            'has_persian_sub': movie_data['has_persian_sub']
        }
    
    def get_random_movies(self, count: int = 5) -> List[int]:
        """انتخاب تصادفی فیلم‌ها"""
        random_movies = self.movies_df.sample(n=min(count, len(self.movies_df)))
        return random_movies['id'].tolist()

# مثال استفاده
if __name__ == "__main__":
    engine = RecommendationEngine()
    
    # ایجاد ترجیحات نمونه کاربر
    user_prefs = UserPreference(
        user_id=1,
        preferred_genres=['اکشن', 'درام'],
        min_rating=7.0,
        preferred_years=[2020, 2021, 2022],
        language_preference='both',
        watched_movies=[],
        liked_movies=[],
        disliked_movies=[]
    )
    
    # دریافت پیشنهادات
    recommendations = engine.collaborative_filtering(user_prefs, top_k=5)
    print("پیشنهادات:", recommendations)
    
    # دریافت فیلم‌های ترند
    trending = engine.get_trending_movies(5)
    print("فیلم‌های ترند:", trending)
